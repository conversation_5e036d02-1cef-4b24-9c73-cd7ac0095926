import camelcaseKeys from 'camelcase-keys';

// Utility to convert snake_case to camelCase for API responses
export function toCamelCase<T>(obj: any): T {
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => toCamelCase(item)) as T;
  }
  
  if (typeof obj === 'object' && obj.constructor === Object) {
    return camelcaseKeys(obj, { deep: true }) as T;
  }
  
  return obj;
}

// Utility to convert camelCase to snake_case for API requests
export function toSnakeCase(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => toSnakeCase(item));
  }
  
  if (typeof obj === 'object' && obj.constructor === Object) {
    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
      result[snakeKey] = toSnakeCase(value);
    }
    return result;
  }
  
  return obj;
}

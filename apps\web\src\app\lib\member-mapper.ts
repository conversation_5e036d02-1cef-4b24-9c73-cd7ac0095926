import { formatApiAccount, Group, Member } from "@repo/ui";
import { getAccounts } from "./account";
import { getAccessibleGroups, getAccessibleUsers } from "./user";

export interface ApiAccount {
  accountId: string;
  linkedAccRef: string;
  maskedAccNumber: string;
  fipId: string;
  holderName: string;
  branch: string;
  fiType: string;
  userId: string;
  dataSyncedAt: string;
}

export interface GetAccountsResponse {
  accounts: ApiAccount[];
}

export interface ApiUser {
  id: {
    id: string;
  };
  phoneNumber: string;
  pan?: string;
  name: string;
  address?: string;
  dateOfBirth?: string;
  familyId?: string;
  groupId?: string | null;
  isPrimary?: boolean;
  lastLoginTime?: string;
  createdAt?: string;
  updatedAt?: string;
  nickname?: string;
  email?: string;
  relation?: string;
  alternatePhoneNumbers?: string[];
}

export interface ApiGroup {
  group: {
    id: {
      id: string;
    };
    name: string;
    familyId?: string;
    createdAt?: string;
    updatedAt?: string;
    createdBy?: string;
  };
  users: ApiUser[];
}

export interface GetAccessibleUsersResponse {
  users: ApiUser[];
}

export interface GetAccessibleGroupsResponse {
  groups: ApiGroup[];
}
export async function mapApiResponseToMembersServerWithAccounts(
  response: GetAccessibleUsersResponse,
  authToken: string,
): Promise<Member[]> {
  try {
    const accountsResponse = await getAccounts(authToken);
    const allAccounts = accountsResponse.accounts || [];
    const userAccountsMap: Record<string, ApiAccount[]> = {};
    allAccounts.forEach((account) => {
      if (!userAccountsMap[account.userId]) {
        userAccountsMap[account.userId] = [];
      }
      userAccountsMap[account.userId]!.push(formatApiAccount(account));
    });

    const members = response.users
      .map((user) => {
        // Handle both old (_id) and new (id) structure, and direct id vs nested id
        const userId = (user as any)._id?.id || user.id?.id || user.id || (user as any)._id;
        if (!userId) {
          return null;
        }
        const userAccounts = userAccountsMap[userId] || [];

        if (userAccounts.length === 0) {
          return null;
        }

        return {
          id: userId,
          name: user.name?.trim()
            ? user.name
            : userAccounts[0]?.holderName || user.phoneNumber,
          accounts: userAccounts,
        };
      })

      .filter(Boolean) as Member[];

    return members;
  } catch (error) {
    console.error("Error mapping members with accounts:", error);
    return [];
  }
}

export async function mapApiResponseToGroupsServerWithAccounts(
  response: GetAccessibleGroupsResponse,
  authToken: string,
): Promise<Group[]> {
  try {
    const accountsResponse = await getAccounts(authToken);
    const allAccounts = accountsResponse.accounts || [];
    const userAccountsMap: Record<string, ApiAccount[]> = {};
    allAccounts.forEach((account) => {
      if (!userAccountsMap[account.userId]) {
        userAccountsMap[account.userId] = [];
      }
      userAccountsMap[account.userId]!.push(formatApiAccount(account));
    });

    const groups = response.groups
      .map((apiGroup) => {
        const members = apiGroup.users
          .map((user) => {
            // Handle both old (_id) and new (id) structure, and direct id vs nested id
            const userId = (user as any)._id?.id || user.id?.id || user.id || (user as any)._id;
            if (!userId) {
              return null;
            }
            const userAccounts = userAccountsMap[userId] || [];

            if (userAccounts.length === 0) {
              return null;
            }

            return {
              id: userId,
              name: user.name?.trim()
                ? user.name
                : userAccounts[0]?.holderName || user.phoneNumber,
              accounts: userAccounts,
            };
          })

          .filter(Boolean) as Member[];

        if (members.length === 0) {
          return null;
        }

        return {
          id: (apiGroup.group as any)._id?.id || apiGroup.group.id?.id || apiGroup.group.id || (apiGroup.group as any)._id,
          groupName: apiGroup.group.name,
          members: members,
        };
      })
      .filter(Boolean) as Group[];

    return groups;
  } catch (error) {
    console.error("Error mapping groups with accounts:", error);
    return [];
  }
}
export async function getMembersAndGroups(authToken: string) {
  try {
    const [usersResponse, groupsResponse] = await Promise.all([
      getAccessibleUsers(authToken),
      getAccessibleGroups(authToken),
    ]);

    const [members, groups] = await Promise.all([
      mapApiResponseToMembersServerWithAccounts(usersResponse, authToken),
      mapApiResponseToGroupsServerWithAccounts(groupsResponse, authToken),
    ]);

    return { members, groups };
  } catch (error) {
    console.error("Failed to get members and groups:", error);
    return { members: [], groups: [] };
  }
}

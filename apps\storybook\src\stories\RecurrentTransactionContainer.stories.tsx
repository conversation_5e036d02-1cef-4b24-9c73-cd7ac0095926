import { Meta, StoryObj } from "@storybook/react";
import {
  creditCategories,
  debitCategories,
  dummyAccount,
  dummyRecurrentTransactions,
  RecurrentTransactionCard,
  RecurrentTxnContainer,
} from "@repo/ui";

import camelcaseKeys from "camelcase-keys";
const camelizedTransactions = camelcaseKeys(dummyRecurrentTransactions, {
  deep: true,
});
const camelizedAccounts = camelcaseKeys(dummyAccount, { deep: true });

const enhancedTxns = camelizedTransactions.txns.map((txn) => {
  const matchingAccount = camelizedAccounts.accounts.find(
    (acc) => acc.accountId.trim() === txn.txnCard.accountId,
  );

  if (matchingAccount) {
    const newTxnCard = {
      ...txn.txnCard,
      account: matchingAccount,
    };

    return {
      ...txn,
      txnCard: newTxnCard,
    };
  }

  return txn;
});

const enhancedTransactions = {
  summaries: camelizedTransactions.summaries,
  txns: enhancedTxns as RecurrentTransactionCard[],
};
const categories = [...creditCategories, ...debitCategories];

const meta: Meta<typeof RecurrentTxnContainer> = {
  title: "Recurrent Transaction/Recurrent Transactions Container",
  component: RecurrentTxnContainer,
  tags: ["autodocs"],
  args: {
    transactions: enhancedTransactions,
    categories: categories,
  },
};
export default meta;
type Story = StoryObj<typeof RecurrentTxnContainer>;

export const Default: Story = {};

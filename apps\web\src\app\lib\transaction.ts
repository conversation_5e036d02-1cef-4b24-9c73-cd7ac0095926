import * as grpc from "@grpc/grpc-js";
import * as protoLoader from "@grpc/proto-loader";
import path from "path";
import {
  Account,
  Category,
  FetchDocuments,
  Subcategory,
  TransactionTypeEnum,
} from "@repo/ui";
import { toSnakeCase } from "./utils";

export enum SearchBy {
  SEARCH_BY_ALL = 0,
  SEARCH_BY_TAG = 1,
  SEARCH_BY_MERCHANT = 2,
  SEARCH_BY_USER = 3,
  SEARCH_BY_NOTES = 4,
  SEARCH_BY_AMOUNT = 5,
  SEARCH_BY_REMARKS = 6,
}

interface TransactionCategory {
  categoryCollection: string;
  categoryId: string;
  subcategoryId: string;
}

interface TransactionMerchant {
  userId: string;
  merchantId: string;
  merchantName: string;
}

export interface Transaction {
  txnId: string;
  accountId: string;
  amount: number;
  type: TransactionTypeEnum;
  txnTimestamp: string;
  mode: string;
  narration: string;
  rawTxnId: string;
  favorite: boolean;
  tag: TransactionCategory;
  excludeCashFlow: boolean;
  merchant: TransactionMerchant;
  fipId: string;
  userNotes: string;
  cashFlowPeriod: {
    month: number;
    year: number;
  };
  documentsCount: number;

  account?: Account;
  category?: Category;
  subcategory?: Subcategory;
}

export interface GetTransactionResponse {
  cards: Transaction[];
}

type DepositTransactionClient = grpc.Client & {
  FetchDepositTxns: (
    request: object,
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: GetTransactionResponse,
    ) => void,
  ) => void;
  MarkDepositTxnFavorite: (
    request: {
      transactionId: string;
      favorite: boolean;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  ExcludeTxnFromCashFlow: (
    request: {
      txnId: string;
      excludeCashFlow: boolean;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  UpdateTransactionNotes: (
    request: {
      transactionId: string;
      notes: string;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  FetchTransactionDocuments: (
    request: {
      transactionId: string;
    },
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: FetchDocuments,
    ) => void,
  ) => void;
  DeleteTransactionDocuments: (
    request: {
      transactionId: string;
      objectNames: string[];
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  AssignCategoryToDepositTxns: (
    request: {
      transactionIds: string[];
      categoryId: string;
      collection: string;
      subcategoryId: string;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  SearchTxns: (
    request: {
      input: string;
      searchBy: SearchBy;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
};

const PROTO_DIR_PATH: string = process.env.PROTO_DIR_PATH || "";
const PROTO_FILE: string =
  "backend_services/visualization/deposit_transaction.proto";

const TRANSACTION_PROTO_FILE_PATH: string = path.resolve(
  PROTO_DIR_PATH,
  PROTO_FILE,
);

const options: protoLoader.Options = {
  keepCase: false,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true,
  includeDirs: [path.resolve(PROTO_DIR_PATH)],
};

let cachedTransactionClient: DepositTransactionClient | null = null;

const packageDefinition = protoLoader.loadSync(
  TRANSACTION_PROTO_FILE_PATH,
  options,
);
const txnProto = grpc.loadPackageDefinition(packageDefinition) as any;

const transactionService = txnProto.backend_services.visualization;

const requestMetadata = new grpc.Metadata();

export function createTransactionServiceClient() {
  if (cachedTransactionClient) {
    return cachedTransactionClient;
  }
  const target: string = process.env.GRPC_SERVER_URI || "";
  const client = new transactionService.DepositTransaction(
    target,
    grpc.credentials.createSsl(),
  ) as DepositTransactionClient;
  cachedTransactionClient = client;
  return client;
}

interface UserId {
  id: string;
}
interface UserGroups {
  userIds: UserId[];
  userGroupIds: UserId[];
}

const transactionClient: DepositTransactionClient =
  createTransactionServiceClient();

export function getTransactions(
  authToken: string,
  pageSize: number,
  pageNumber: number,
  filters?: {
    userGroups?: UserGroups;
    accountFilters?: { accountIds?: string[] };
    timeRange?: { fromTime: number; toTime: number };
    amountRange?: { minAmount: number; maxAmount: number };
    transactionType?: string;
    tagStatus?: string;
    bookmarkOptions?: {
      showFavorites?: boolean;
      excludedFromCashflow?: boolean;
      withNotes?: boolean;
    };
    categories?: Array<{
      categoryCollection: string;
      categoryId: string;
      subcategoryId: string;
    }>;
  },
): Promise<GetTransactionResponse> {
  requestMetadata.set("authorization", authToken);
  const request: any = {
    filter: {},
  };

  if (filters) {
    if (
      filters.userGroups?.userIds?.length ||
      filters.userGroups?.userGroupIds?.length
    ) {
      request.filter.user_groups = {};

      if (filters.userGroups?.userIds?.length) {
        request.filter.user_groups.user_ids = filters.userGroups.userIds;
      }
      if (filters.userGroups?.userGroupIds?.length) {
        request.filter.user_groups.user_group_ids =
          filters.userGroups.userGroupIds;
      }
    }

    if (filters.accountFilters?.accountIds?.length) {
      request.filter.account_filters = {
        account_ids: filters.accountFilters.accountIds.map((id: any) => id),
      };
    }

    request.filter.txn_filters = {};

    // Time range filter
    if (
      filters.timeRange &&
      (filters.timeRange.fromTime > 0 || filters.timeRange.toTime > 0)
    ) {
      request.filter.txn_filters.time_range = {
        from_time:
          filters.timeRange.fromTime > 0
            ? String(filters.timeRange.fromTime)
            : undefined,
        to_time:
          filters.timeRange.toTime > 0
            ? String(filters.timeRange.toTime)
            : undefined,
      };
      if (!request.filter.txn_filters.time_range.from_time) {
        delete request.filter.txn_filters.time_range.from_time;
      }
      if (!request.filter.txn_filters.time_range.to_time) {
        delete request.filter.txn_filters.time_range.to_time;
      }
      if (
        !request.filter.txn_filters.time_range.from_time &&
        !request.filter.txn_filters.time_range.to_time
      ) {
        delete request.filter.txn_filters.time_range;
      }
    }

    // Amount range filter
    if (
      filters.amountRange &&
      ((typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0) ||
        (typeof filters.amountRange.maxAmount === "number" &&
          filters.amountRange.maxAmount > 0))
    ) {
      request.filter.txn_filters.amount_range = {};

      if (
        typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0
      ) {
        request.filter.txn_filters.amount_range.min_amount = String(
          filters.amountRange.minAmount,
        );
      }

      if (
        typeof filters.amountRange.maxAmount === "number" &&
        filters.amountRange.maxAmount > 0
      ) {
        request.filter.txn_filters.amount_range.max_amount = String(
          filters.amountRange.maxAmount,
        );
      }
    }

    // Bookmark options filter
    if (filters.bookmarkOptions) {
      const { showFavorites, excludedFromCashflow, withNotes } =
        filters.bookmarkOptions;

      if (showFavorites) {
        request.filter.txn_filters.favorited = { favorited: true };
      }

      if (excludedFromCashflow) {
        request.filter.txn_filters.exclude_cash_flow = {
          exclude_cash_flow: true,
        };
      }

      if (withNotes) {
        request.filter.txn_filters.has_user_notes = { has_user_notes: true };
      }
    }

    // Transaction type
    if (filters.transactionType && filters.transactionType !== "all") {
      const txnType =
        filters.transactionType.toLowerCase() === "incoming"
          ? "CREDIT"
          : "DEBIT";
      request.filter.txn_filters.txn_type = { txn_type: txnType };
    }

    // Tag status
    if (filters.tagStatus && filters.tagStatus.toLowerCase() !== "all") {
      const isUntagged = filters.tagStatus.toLowerCase() === "untagged";
      request.filter.txn_filters.untagged = { untagged: isUntagged };
    }

    // Categories filter
    if (filters.categories?.length) {
      request.filter.txn_filters.category = filters.categories.map(
        (category) => ({
          category_collection: category.categoryCollection || "global",
          category_id: category.categoryId,
          subcategory_id: category.subcategoryId,
        }),
      );
    }
  }

  request.pagination_params = {
    page_size: pageSize,
    page_number: pageNumber,
  };

  return new Promise((resolve, reject) => {
    transactionClient.FetchDepositTxns(
      toSnakeCase(request),
      requestMetadata,
      (error, response) => {
        if (error) {
          console.error("FetchDepositTxns error:", error);
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function saveTransaction(
  authToken: string,
  transactionId: string,
  favorite: boolean,
): Promise<object> {
  requestMetadata.set("authorization", authToken);
  return new Promise((resolve, reject) => {
    transactionClient.MarkDepositTxnFavorite(
      toSnakeCase({
        transactionId,
        favorite,
      }),
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function assignCategoryToTransaction(
  authToken: string,
  transactionId: string,
  categoryId: string,
  subcategoryId: string,
  collection: string = "",
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.AssignCategoryToDepositTxns(
      toSnakeCase({
        transactionIds: [transactionId],
        categoryId,
        subcategoryId,
        collection,
      }),
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function excludeTransactionFromCashFlow(
  authToken: string,
  transactionId: string,
  flag: boolean,
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.ExcludeTxnFromCashFlow(
      toSnakeCase({
        txnId: transactionId,
        excludeCashFlow: flag,
      }),
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function updateTransactionNotes(
  authToken: string,
  transactionId: string,
  notes: string,
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.UpdateTransactionNotes(
      toSnakeCase({
        transactionId,
        notes,
      }),
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function fetchTransactionDocumentsRPC(
  authToken: string,
  transactionId: string,
): Promise<FetchDocuments> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.FetchTransactionDocuments(
      toSnakeCase({
        transactionId,
      }),
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function deleteTransactionDocumentsRPC(
  authToken: string,
  transactionId: string,
  fileNames: string[],
): Promise<{ ok: boolean }> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.DeleteTransactionDocuments(
      toSnakeCase({
        transactionId,
        objectNames: fileNames,
      }),
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve({
            ok: true,
          });
        }
      },
    );
  });
}

export function searchTransactionRPC(
  authToken: string,
  input: string,
  searchBy: SearchBy,
  pageNumber: number,
  pageSize: number,
  filters?: {
    timeRange?: { fromTime: number; toTime: number };
    amountRange?: { minAmount: number; maxAmount: number };
    transactionType?: string;
    tagStatus?: string;
    bookmarkOptions?: {
      showFavorites?: boolean;
      excludedFromCashflow?: boolean;
      withNotes?: boolean;
    };
  },
): Promise<{ cards: Transaction[] }> {
  requestMetadata.set("authorization", authToken);

  const request: any = {};

  if (filters) {
    request.filter = {
      txn_filters: {},
    };

    // Time range filter
    if (
      filters.timeRange &&
      (filters.timeRange.fromTime > 0 || filters.timeRange.toTime > 0)
    ) {
      request.filter.txn_filters.time_range = {
        from_time:
          filters.timeRange.fromTime > 0
            ? String(filters.timeRange.fromTime)
            : undefined,
        to_time:
          filters.timeRange.toTime > 0
            ? String(filters.timeRange.toTime)
            : undefined,
      };
      if (!request.filter.txn_filters.time_range.from_time) {
        delete request.filter.txn_filters.time_range.from_time;
      }
      if (!request.filter.txn_filters.time_range.to_time) {
        delete request.filter.txn_filters.time_range.to_time;
      }
      if (
        !request.filter.txn_filters.time_range.from_time &&
        !request.filter.txn_filters.time_range.to_time
      ) {
        delete request.filter.txn_filters.time_range;
      }
    }

    // Amount range filter
    if (
      filters.amountRange &&
      ((typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0) ||
        (typeof filters.amountRange.maxAmount === "number" &&
          filters.amountRange.maxAmount > 0))
    ) {
      request.filter.txn_filters.amount_range = {};

      if (
        typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0
      ) {
        request.filter.txn_filters.amount_range.min_amount = String(
          filters.amountRange.minAmount,
        );
      }

      if (
        typeof filters.amountRange.maxAmount === "number" &&
        filters.amountRange.maxAmount > 0
      ) {
        request.filter.txn_filters.amount_range.max_amount = String(
          filters.amountRange.maxAmount,
        );
      }
    }

    // Bookmark options filter
    if (filters.bookmarkOptions) {
      const { showFavorites, excludedFromCashflow, withNotes } =
        filters.bookmarkOptions;

      if (showFavorites) {
        request.filter.txn_filters.favorited = { favorited: true };
      }

      if (excludedFromCashflow) {
        request.filter.txn_filters.exclude_cash_flow = {
          exclude_cash_flow: true,
        };
      }

      if (withNotes) {
        request.filter.txn_filters.has_user_notes = { has_user_notes: true };
      }
    }
    // Transaction type
    if (filters.transactionType && filters.transactionType !== "all") {
      const txnType =
        filters.transactionType.toLowerCase() === "incoming"
          ? "CREDIT"
          : "DEBIT";
      request.filter.txn_filters.txn_type = { txn_type: txnType };
    }

    // Tag status
    if (filters.tagStatus && filters.tagStatus.toLowerCase() !== "all") {
      const isUntagged = filters.tagStatus.toLowerCase() === "untagged";
      request.filter.txn_filters.untagged = { untagged: isUntagged };
    }
  }
  request.input = input;
  request.search_by = searchBy;
  request.pagination_params = {
    page_size: pageSize,
    page_number: pageNumber,
  };

  return new Promise((resolve, reject) => {
    transactionClient.SearchTxns(
      toSnakeCase(request),
      requestMetadata,
      (error, response: any) => {
        if (error) {
          reject(error);
        } else {
          resolve({
            cards: response.cards,
          });
        }
      },
    );
  });
}

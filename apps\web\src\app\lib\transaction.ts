import * as grpc from "@grpc/grpc-js";
import * as protoLoader from "@grpc/proto-loader";
import path from "path";
import {
  Account,
  Category,
  FetchDocuments,
  Subcategory,
  TransactionTypeEnum,
} from "@repo/ui";


export enum SearchBy {
  SEARCH_BY_ALL = 0,
  SEARCH_BY_TAG = 1,
  SEARCH_BY_MERCHANT = 2,
  SEARCH_BY_USER = 3,
  SEARCH_BY_NOTES = 4,
  SEARCH_BY_AMOUNT = 5,
  SEARCH_BY_REMARKS = 6,
}

interface TransactionCategory {
  categoryCollection: string;
  categoryId: string;
  subcategoryId: string;
}

interface TransactionMerchant {
  userId: string;
  merchantId: string;
  merchantName: string;
}

export interface Transaction {
  txnId: string;
  accountId: string;
  amount: number;
  type: TransactionTypeEnum;
  txnTimestamp: string;
  mode: string;
  narration: string;
  rawTxnId: string;
  favorite: boolean;
  tag: TransactionCategory;
  excludeCashFlow: boolean;
  merchant: TransactionMerchant;
  fipId: string;
  userNotes: string;
  cashFlowPeriod: {
    month: number;
    year: number;
  };
  documentsCount: number;

  account?: Account;
  category?: Category;
  subcategory?: Subcategory;
}

export interface GetTransactionResponse {
  cards: Transaction[];
}

type DepositTransactionClient = grpc.Client & {
  FetchDepositTxns: (
    request: object,
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: GetTransactionResponse,
    ) => void,
  ) => void;
  MarkDepositTxnFavorite: (
    request: {
      transactionId: string;
      favorite: boolean;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  ExcludeTxnFromCashFlow: (
    request: {
      txnId: string;
      excludeCashFlow: boolean;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  UpdateTransactionNotes: (
    request: {
      transactionId: string;
      notes: string;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  FetchTransactionDocuments: (
    request: {
      transactionId: string;
    },
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: FetchDocuments,
    ) => void,
  ) => void;
  DeleteTransactionDocuments: (
    request: {
      transactionId: string;
      objectNames: string[];
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  AssignCategoryToDepositTxns: (
    request: {
      transactionIds: string[];
      categoryId: string;
      collection: string;
      subcategoryId: string;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  SearchTxns: (
    request: {
      input: string;
      searchBy: SearchBy;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
};

const PROTO_DIR_PATH: string = process.env.PROTO_DIR_PATH || "";
const PROTO_FILE: string =
  "backend_services/visualization/deposit_transaction.proto";

const TRANSACTION_PROTO_FILE_PATH: string = path.resolve(
  PROTO_DIR_PATH,
  PROTO_FILE,
);

const options: protoLoader.Options = {
  keepCase: false,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true,
  includeDirs: [path.resolve(PROTO_DIR_PATH)],
};

let cachedTransactionClient: DepositTransactionClient | null = null;

const packageDefinition = protoLoader.loadSync(
  TRANSACTION_PROTO_FILE_PATH,
  options,
);
const txnProto = grpc.loadPackageDefinition(packageDefinition) as any;

const transactionService = txnProto.backend_services.visualization;

const requestMetadata = new grpc.Metadata();

export function createTransactionServiceClient() {
  if (cachedTransactionClient) {
    return cachedTransactionClient;
  }
  const target: string = process.env.GRPC_SERVER_URI || "";
  const client = new transactionService.DepositTransaction(
    target,
    grpc.credentials.createSsl(),
  ) as DepositTransactionClient;
  cachedTransactionClient = client;
  return client;
}

interface UserId {
  id: string;
}
interface UserGroups {
  userIds: UserId[];
  userGroupIds: UserId[];
}

const transactionClient: DepositTransactionClient =
  createTransactionServiceClient();

export function getTransactions(
  authToken: string,
  pageSize: number,
  pageNumber: number,
  filters?: {
    userGroups?: UserGroups;
    accountFilters?: { accountIds?: string[] };
    timeRange?: { fromTime: number; toTime: number };
    amountRange?: { minAmount: number; maxAmount: number };
    transactionType?: string;
    tagStatus?: string;
    bookmarkOptions?: {
      showFavorites?: boolean;
      excludedFromCashflow?: boolean;
      withNotes?: boolean;
    };
    categories?: Array<{
      categoryCollection: string;
      categoryId: string;
      subcategoryId: string;
    }>;
  },
): Promise<GetTransactionResponse> {
  requestMetadata.set("authorization", authToken);
  const request: any = {
    filter: {},
  };

  if (filters) {
    if (
      filters.userGroups?.userIds?.length ||
      filters.userGroups?.userGroupIds?.length
    ) {
      request.filter.userGroups = {};

      if (filters.userGroups?.userIds?.length) {
        request.filter.userGroups.userIds = filters.userGroups.userIds;
      }
      if (filters.userGroups?.userGroupIds?.length) {
        request.filter.userGroups.userGroupIds =
          filters.userGroups.userGroupIds;
      }
    }

    if (filters.accountFilters?.accountIds?.length) {
      request.filter.accountFilters = {
        accountIds: filters.accountFilters.accountIds.map((id: any) => id),
      };
    }

    request.filter.txnFilters = {};

    // Time range filter
    if (
      filters.timeRange &&
      (filters.timeRange.fromTime > 0 || filters.timeRange.toTime > 0)
    ) {
      request.filter.txnFilters.timeRange = {
        fromTime:
          filters.timeRange.fromTime > 0
            ? String(filters.timeRange.fromTime)
            : undefined,
        toTime:
          filters.timeRange.toTime > 0
            ? String(filters.timeRange.toTime)
            : undefined,
      };
      if (!request.filter.txnFilters.timeRange.fromTime) {
        delete request.filter.txnFilters.timeRange.fromTime;
      }
      if (!request.filter.txnFilters.timeRange.toTime) {
        delete request.filter.txnFilters.timeRange.toTime;
      }
      if (
        !request.filter.txnFilters.timeRange.fromTime &&
        !request.filter.txnFilters.timeRange.toTime
      ) {
        delete request.filter.txnFilters.timeRange;
      }
    }

    // Amount range filter
    if (
      filters.amountRange &&
      ((typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0) ||
        (typeof filters.amountRange.maxAmount === "number" &&
          filters.amountRange.maxAmount > 0))
    ) {
      request.filter.txnFilters.amountRange = {};

      if (
        typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0
      ) {
        request.filter.txnFilters.amountRange.minAmount = String(
          filters.amountRange.minAmount,
        );
      }

      if (
        typeof filters.amountRange.maxAmount === "number" &&
        filters.amountRange.maxAmount > 0
      ) {
        request.filter.txnFilters.amountRange.maxAmount = String(
          filters.amountRange.maxAmount,
        );
      }
    }

    // Bookmark options filter
    if (filters.bookmarkOptions) {
      const { showFavorites, excludedFromCashflow, withNotes } =
        filters.bookmarkOptions;

      if (showFavorites) {
        request.filter.txnFilters.favorited = { favorited: true };
      }

      if (excludedFromCashflow) {
        request.filter.txnFilters.excludeCashFlow = {
          excludeCashFlow: true,
        };
      }

      if (withNotes) {
        request.filter.txnFilters.hasUserNotes = { hasUserNotes: true };
      }
    }

    // Transaction type
    if (filters.transactionType && filters.transactionType !== "all") {
      const txnType =
        filters.transactionType.toLowerCase() === "incoming"
          ? "CREDIT"
          : "DEBIT";
      request.filter.txnFilters.txnType = { txnType: txnType };
    }

    // Tag status
    if (filters.tagStatus && filters.tagStatus.toLowerCase() !== "all") {
      const isUntagged = filters.tagStatus.toLowerCase() === "untagged";
      request.filter.txnFilters.untagged = { untagged: isUntagged };
    }

    // Categories filter
    if (filters.categories?.length) {
      request.filter.txnFilters.category = filters.categories.map(
        (category) => ({
          categoryCollection: category.categoryCollection || "global",
          categoryId: category.categoryId,
          subcategoryId: category.subcategoryId,
        }),
      );
    }
  }

  request.paginationParams = {
    pageSize: pageSize,
    pageNumber: pageNumber,
  };

  console.log("getTransactions - Final gRPC request:", JSON.stringify(request, null, 2));

  return new Promise((resolve, reject) => {
    transactionClient.FetchDepositTxns(
      request,
      requestMetadata,
      (error, response) => {
        if (error) {
          console.error("FetchDepositTxns error:", error);
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function saveTransaction(
  authToken: string,
  transactionId: string,
  favorite: boolean,
): Promise<object> {
  requestMetadata.set("authorization", authToken);
  return new Promise((resolve, reject) => {
    transactionClient.MarkDepositTxnFavorite(
      {
        transactionId,
        favorite,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function assignCategoryToTransaction(
  authToken: string,
  transactionId: string,
  categoryId: string,
  subcategoryId: string,
  collection: string = "",
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.AssignCategoryToDepositTxns(
      {
        transactionIds: [transactionId],
        categoryId,
        subcategoryId,
        collection,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function excludeTransactionFromCashFlow(
  authToken: string,
  transactionId: string,
  flag: boolean,
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.ExcludeTxnFromCashFlow(
      {
        txnId: transactionId,
        excludeCashFlow: flag,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function updateTransactionNotes(
  authToken: string,
  transactionId: string,
  notes: string,
): Promise<object> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.UpdateTransactionNotes(
      {
        transactionId,
        notes,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function fetchTransactionDocumentsRPC(
  authToken: string,
  transactionId: string,
): Promise<FetchDocuments> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.FetchTransactionDocuments(
      {
        transactionId,
      },
      requestMetadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function deleteTransactionDocumentsRPC(
  authToken: string,
  transactionId: string,
  fileNames: string[],
): Promise<{ ok: boolean }> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.DeleteTransactionDocuments(
      {
        transactionId,
        objectNames: fileNames,
      },
      requestMetadata,
      (error, _response) => {
        if (error) {
          reject(error);
        } else {
          resolve({
            ok: true,
          });
        }
      },
    );
  });
}

export function searchTransactionRPC(
  authToken: string,
  input: string,
  searchBy: SearchBy,
  pageNumber: number,
  pageSize: number,
  filters?: {
    timeRange?: { fromTime: number; toTime: number };
    amountRange?: { minAmount: number; maxAmount: number };
    transactionType?: string;
    tagStatus?: string;
    bookmarkOptions?: {
      showFavorites?: boolean;
      excludedFromCashflow?: boolean;
      withNotes?: boolean;
    };
    categories?: Array<{
      categoryCollection: string;
      categoryId: string;
      subcategoryId: string;
    }>;
  },
): Promise<{ cards: Transaction[] }> {
  requestMetadata.set("authorization", authToken);

  const request: any = {};

  if (filters) {
    request.filter = {
      txnFilters: {},
    };

    // Time range filter
    if (
      filters.timeRange &&
      (filters.timeRange.fromTime > 0 || filters.timeRange.toTime > 0)
    ) {
      request.filter.txnFilters.timeRange = {
        fromTime:
          filters.timeRange.fromTime > 0
            ? String(filters.timeRange.fromTime)
            : undefined,
        toTime:
          filters.timeRange.toTime > 0
            ? String(filters.timeRange.toTime)
            : undefined,
      };
      if (!request.filter.txnFilters.timeRange.fromTime) {
        delete request.filter.txnFilters.timeRange.fromTime;
      }
      if (!request.filter.txnFilters.timeRange.toTime) {
        delete request.filter.txnFilters.timeRange.toTime;
      }
      if (
        !request.filter.txnFilters.timeRange.fromTime &&
        !request.filter.txnFilters.timeRange.toTime
      ) {
        delete request.filter.txnFilters.timeRange;
      }
    }

    // Amount range filter
    if (
      filters.amountRange &&
      ((typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0) ||
        (typeof filters.amountRange.maxAmount === "number" &&
          filters.amountRange.maxAmount > 0))
    ) {
      request.filter.txnFilters.amountRange = {};

      if (
        typeof filters.amountRange.minAmount === "number" &&
        filters.amountRange.minAmount > 0
      ) {
        request.filter.txnFilters.amountRange.minAmount = String(
          filters.amountRange.minAmount,
        );
      }

      if (
        typeof filters.amountRange.maxAmount === "number" &&
        filters.amountRange.maxAmount > 0
      ) {
        request.filter.txnFilters.amountRange.maxAmount = String(
          filters.amountRange.maxAmount,
        );
      }
    }

    // Bookmark options filter
    if (filters.bookmarkOptions) {
      const { showFavorites, excludedFromCashflow, withNotes } =
        filters.bookmarkOptions;

      if (showFavorites) {
        request.filter.txnFilters.favorited = { favorited: true };
      }

      if (excludedFromCashflow) {
        request.filter.txnFilters.excludeCashFlow = {
          excludeCashFlow: true,
        };
      }

      if (withNotes) {
        request.filter.txnFilters.hasUserNotes = { hasUserNotes: true };
      }
    }
    // Transaction type
    if (filters.transactionType && filters.transactionType !== "all") {
      const txnType =
        filters.transactionType.toLowerCase() === "incoming"
          ? "CREDIT"
          : "DEBIT";
      request.filter.txnFilters.txnType = { txnType: txnType };
    }

    // Tag status
    if (filters.tagStatus && filters.tagStatus.toLowerCase() !== "all") {
      const isUntagged = filters.tagStatus.toLowerCase() === "untagged";
      request.filter.txnFilters.untagged = { untagged: isUntagged };
    }

    // Categories filter
    if (filters.categories?.length) {
      request.filter.txnFilters.category = filters.categories.map(
        (category) => ({
          categoryCollection: category.categoryCollection || "global",
          categoryId: category.categoryId,
          subcategoryId: category.subcategoryId,
        }),
      );
    }
  }
  request.input = input;
  request.searchBy = searchBy;
  request.paginationParams = {
    pageSize: pageSize,
    pageNumber: pageNumber,
  };

  return new Promise((resolve, reject) => {
    transactionClient.SearchTxns(
      request,
      requestMetadata,
      (error, response: any) => {
        if (error) {
          reject(error);
        } else {
          resolve({
            cards: response.cards,
          });
        }
      },
    );
  });
}

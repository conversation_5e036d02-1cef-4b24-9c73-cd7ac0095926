import * as grpc from "@grpc/grpc-js";
import * as protoLoader from "@grpc/proto-loader";
import { Account } from "@repo/ui";
import path from "path";

const PROTO_DIR_PATH: string = process.env.PROTO_DIR_PATH || "";
const PROTO_FILE: string = "backend_services/user/settings.proto";

const options: protoLoader.Options = {
  keepCase: false,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true,
  includeDirs: [path.resolve(PROTO_DIR_PATH)],
};

const requestMetadata = new grpc.Metadata();
interface GetAccountsResponse {
  accounts: Account[];
}
type AccountClient = grpc.Client & {
  GetAccounts: (
    request: object,
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: GetAccountsResponse,
    ) => void,
  ) => void;
};

const ACCOUNT_PROTO_FILE_PATH: string = path.resolve(
  PROTO_DIR_PATH,
  PROTO_FILE,
);

let cachedAccountClient: AccountClient | null = null;

const packageDefinition = protoLoader.loadSync(
  ACCOUNT_PROTO_FILE_PATH,
  options,
);
const txnProto = grpc.loadPackageDefinition(packageDefinition) as any;

const accountService = txnProto.backend_services.user;

export function createAccountServiceClient() {
  if (cachedAccountClient) {
    return cachedAccountClient;
  }
  const target: string = process.env.GRPC_SERVER_URI || "";
  const client = new accountService.Settings(
    target,
    grpc.credentials.createSsl(),
  ) as AccountClient;
  cachedAccountClient = client;
  return client;
}

const accountClient: AccountClient = createAccountServiceClient();

export function getAccounts(authToken: string): Promise<GetAccountsResponse> {
  requestMetadata.set("authorization", authToken);
  return new Promise((resolve, reject) => {
    accountClient.GetAccounts({}, requestMetadata, (error, response) => {
      if (error) {
        reject(error);
      } else {
        resolve(response);
      }
    });
  });
}

import * as grpc from "@grpc/grpc-js";
import * as protoLoader from "@grpc/proto-loader";
import path from "path";
import {
  GetAccessibleGroupsResponse,
  GetAccessibleUsersResponse,
} from "./member-mapper";

type UserServiceClient = grpc.Client & {
  GetAccessibleUsers: (
    request: object,
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: GetAccessibleUsersResponse,
    ) => void,
  ) => void;
  GetAccessibleGroups: (
    request: object,
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: GetAccessibleGroupsResponse,
    ) => void,
  ) => void;
};

const PROTO_DIR_PATH: string = process.env.PROTO_DIR_PATH || "";
const USER_PROTO_FILE: string = "backend_services/user/user.proto";

const USER_PROTO_FILE_PATH: string = path.resolve(
  PROTO_DIR_PATH,
  USER_PROTO_FILE,
);

const options: protoLoader.Options = {
  keepCase: false,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true,
  includeDirs: [path.resolve(PROTO_DIR_PATH)],
};

let cachedUserClient: UserServiceClient | null = null;

const packageDefinition = protoLoader.loadSync(USER_PROTO_FILE_PATH, options);
const userProto = grpc.loadPackageDefinition(packageDefinition) as any;
const userService = userProto.backend_services.user;

const requestMetadata = new grpc.Metadata();

export function createUserServiceClient() {
  if (cachedUserClient) {
    return cachedUserClient;
  }
  const target: string = process.env.GRPC_SERVER_URI || "";
  const client = new userService.User(
    target,
    grpc.credentials.createSsl(),
  ) as UserServiceClient;
  cachedUserClient = client;
  return client;
}

const userClient: UserServiceClient = createUserServiceClient();

export function getAccessibleUsers(
  authToken: string,
): Promise<GetAccessibleUsersResponse> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    userClient.GetAccessibleUsers({}, requestMetadata, (error, response) => {
      if (error) {
        console.error("GetAccessibleUsers error:", error);
        reject(error);
      } else {
        resolve(response);
      }
    });
  });
}

export function getAccessibleGroups(
  authToken: string,
): Promise<GetAccessibleGroupsResponse> {
  requestMetadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    userClient.GetAccessibleGroups({}, requestMetadata, (error, response) => {
      if (error) {
        console.error("GetAccessibleGroups error:", error);
        reject(error);
      } else {
        resolve(response);
      }
    });
  });
}

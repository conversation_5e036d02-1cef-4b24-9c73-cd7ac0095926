"use client";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "./ui/tabs";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "./ui/accordion";
import { Checkbox } from "./ui/checkbox";
import { cn } from "../lib/utils";
import { SyncIcon } from "../icons/sync";
import SvgHdfc from "../icons/bank/hdfc";
import SvgIcici from "../icons/bank/icici";
import SvgAxis from "../icons/bank/axis";
import SvgSbi from "../icons/bank/sbi";
import { Landmark } from "lucide-react";
import { format } from "date-fns";
import { Account } from "./TransactionsTable";

export interface Member {
  id: string;
  name: string;
  accounts: Account[];
}

export interface Group {
  id: string;
  groupName: string;
  members: Member[];
}
export interface SelectedAccountsType {
  accountIds: Set<string>;
}

export interface MemberData {
  memberId: string;
  memberName: string;
  accounts: Account[];
}

export interface GroupData {
  groupId: string;
  groupName: string;
  members: MemberData[];
}

interface BankIconProps {
  bankName?: string;
  className?: string;
}
export const BankIconMapping: React.FC<BankIconProps> = ({
  bankName = "bank",
  className,
}) => {
  const bank = (bankName || "").toLowerCase();

  if (bank.includes("icici bank")) {
    return <SvgIcici className={className} />;
  }

  if (bank.includes("hdfc")) {
    return <SvgHdfc className={className} />;
  }

  if (bank.includes("axis")) {
    return <SvgAxis className={className} />;
  }

  if (bank.includes("sbi")) {
    return <SvgSbi className={className} />;
  }

  return <Landmark className={className} />;
};

interface MemberGroupProps {
  members: Member[];
  groups: Group[];
  selectedMembers: string[];
  selectedGroups: string[];
  selectedAccounts: SelectedAccountsType;
  onSelectionChange?: (
    memberData: MemberData[],
    groupData: GroupData[],
    selectedAccounts: SelectedAccountsType,
  ) => void;
}

export function MemberGroupFilter({
  members,
  groups,
  selectedMembers,
  selectedGroups,
  selectedAccounts,
  onSelectionChange,
}: MemberGroupProps) {
  const [selectedAccount, setSelectedAccount] = useState<SelectedAccountsType>({
    accountIds: new Set(Object.keys(selectedAccounts.accountIds || new Set())),
  });
  const [selectedMemberAndGroupIds, setSelectedMemberAndGroupIds] = useState<
    string[]
  >([...selectedMembers, ...selectedGroups]);

  const handleSelectionChange = (
    selectedIds: string[],
    accounts: SelectedAccountsType,
  ) => {
    const memberData = members
      .filter((mem) => selectedIds.includes(mem.id))
      .map((mem) => ({
        memberId: mem.id,
        memberName: mem.name,
        accounts: mem.accounts,
      }));

    const groupData = groups
      .filter((grp) => selectedIds.includes(grp.id))
      .map((grp) => ({
        groupId: grp.id,
        groupName: grp.groupName,
        members: grp.members.map((mem) => ({
          memberId: mem.id,
          memberName: mem.name,
          accounts: mem.accounts,
        })),
      }));

    onSelectionChange?.(memberData, groupData, accounts);
  };

  useEffect(() => {
    const currentAccountIds = [...selectedAccount.accountIds].sort();
    const nextAccountIds = [
      ...(selectedAccounts.accountIds || new Set()),
    ].sort();
    const isAccountsInSync =
      JSON.stringify(currentAccountIds) === JSON.stringify(nextAccountIds);
    const isIdsInSync =
      JSON.stringify([...selectedMemberAndGroupIds].sort()) ===
      JSON.stringify([...selectedMembers, ...selectedGroups].sort());
    if (!isAccountsInSync || !isIdsInSync) {
      setSelectedAccount({
        accountIds: new Set(selectedAccounts.accountIds || []),
      });
      setSelectedMemberAndGroupIds([...selectedMembers, ...selectedGroups]);
    }
  }, [
    selectedMembers,
    selectedGroups,
    selectedAccounts,
    selectedAccount,
    selectedMemberAndGroupIds,
  ]);
  const [openItem, setOpenItem] = useState<string | undefined>(undefined);

  const handleCardClick = (
    account: Account,
    member?: Member,
    group?: Group,
  ) => {
    setSelectedAccount((prevSelectedAccounts) => {
      const newAccountIds = new Set(prevSelectedAccounts.accountIds);
      if (newAccountIds.has(account.accountId!)) {
        newAccountIds.delete(account.accountId!);
      } else {
        newAccountIds.add(account.accountId!);
      }
      const newSelectedAccounts = { accountIds: newAccountIds };
      if (members.length === 1 && groups.length === 0) {
        const singleMember = members[0];
        const anyAccountSelected = singleMember?.accounts.some((acc) =>
          newAccountIds.has(acc.accountId!),
        );
        const updatedMemberIds =
          anyAccountSelected && singleMember?.id ? [singleMember.id] : [];
        setSelectedMemberAndGroupIds(updatedMemberIds);
        onSelectionChange?.(
          anyAccountSelected
            ? [
                {
                  memberId: singleMember!.id,
                  memberName: singleMember!.name,
                  accounts: singleMember!.accounts,
                },
              ]
            : [],
          [],
          newSelectedAccounts,
        );

        return newSelectedAccounts;
      }
      if (member) {
        const anySelected = member.accounts.some((acc) =>
          newAccountIds.has(acc.accountId!),
        );
        setSelectedMemberAndGroupIds((prev) => {
          const updated = anySelected
            ? [...new Set([...prev, member.id])]
            : prev.filter((id) => id !== member.id);
          onSelectionChange?.(
            members
              .filter((mem) => updated.includes(mem.id))
              .map((mem) => ({
                memberId: mem.id,
                memberName: mem.name,
                accounts: mem.accounts,
              })),
            groups
              .filter((grp) => updated.includes(grp.id))
              .map((grp) => ({
                groupId: grp.id,
                groupName: grp.groupName,
                members: grp.members.map((mem) => ({
                  memberId: mem.id,
                  memberName: mem.name,
                  accounts: mem.accounts,
                })),
              })),
            newSelectedAccounts,
          );

          return updated;
        });
      } else if (group) {
        const anySelected = group.members.some((mem) =>
          mem.accounts.some((acc) => newAccountIds.has(acc.accountId!)),
        );
        setSelectedMemberAndGroupIds((prev) => {
          const updated = anySelected
            ? [...new Set([...prev, group.id])]
            : prev.filter((id) => id !== group.id);
          onSelectionChange?.(
            members
              .filter((mem) => updated.includes(mem.id))
              .map((mem) => ({
                memberId: mem.id,
                memberName: mem.name,
                accounts: mem.accounts,
              })),
            groups
              .filter((grp) => updated.includes(grp.id))
              .map((grp) => ({
                groupId: grp.id,
                groupName: grp.groupName,
                members: grp.members.map((mem) => ({
                  memberId: mem.id,
                  memberName: mem.name,
                  accounts: mem.accounts,
                })),
              })),
            newSelectedAccounts,
          );
          return updated;
        });
      }
      return newSelectedAccounts;
    });
  };

  const handleMemberCheckboxChange = (member: Member, checked: boolean) => {
    setSelectedAccount((prevSelectedAccounts) => {
      const updatedAccountIds = new Set(prevSelectedAccounts.accountIds);

      member.accounts.forEach((account) => {
        if (checked) {
          updatedAccountIds.add(account.accountId!);
        } else {
          updatedAccountIds.delete(account.accountId!);
        }
      });
      const updatedAccounts = { accountIds: updatedAccountIds };
      setSelectedMemberAndGroupIds((prev) => {
        const hasAnyAccountSelected = member.accounts.some((acc) =>
          updatedAccountIds.has(acc.accountId!),
        );
        const updated = hasAnyAccountSelected
          ? [...new Set([...prev, member.id])]
          : prev.filter((id) => id !== member.id);
        handleSelectionChange(updated, updatedAccounts);
        return updated;
      });
      return updatedAccounts;
    });
  };

  const handleGroupCheckboxChange = (group: Group, checked: boolean) => {
    setSelectedAccount((prevSelectedAccounts) => {
      const updatedAccountIds = new Set(prevSelectedAccounts.accountIds);

      group.members.forEach((member) => {
        member.accounts.forEach((account) => {
          if (checked) {
            updatedAccountIds.add(account.accountId!);
          } else {
            updatedAccountIds.delete(account.accountId!);
          }
        });
      });
      const updatedAccounts = { accountIds: updatedAccountIds };
      setSelectedMemberAndGroupIds((prev) => {
        const hasAnyAccountSelected = group.members.some((mem) =>
          mem.accounts.some((acc) => updatedAccountIds.has(acc.accountId!)),
        );
        const updated = hasAnyAccountSelected
          ? [...new Set([...prev, group.id])]
          : prev.filter((id) => id !== group.id);
        handleSelectionChange(updated, updatedAccounts);
        return updated;
      });
      return updatedAccounts;
    });
  };
  const groupMembersName = (members: Member[]) => {
    const names = members.map((m) => m.name);
    return names.length > 3
      ? `${names.slice(0, 3).join(", ")} +${names.length - 3} more`
      : names.join(", ");
  };

  const AccountCard = ({
    account,
    member,
    group,
  }: {
    account: Account;
    member?: Member;
    group?: Group;
  }) => {
    const isCardSelected =
      selectedAccount.accountIds.size > 0 &&
      selectedAccount.accountIds.has(account.accountId);
    return (
      <div
        key={account.accountId}
        className={cn(
          "flex flex-col p-3 gap-2 rounded-[10px] w-fit h-fit  cursor-pointer",
          isCardSelected ? "bg-[#F4EEF9]" : "bg-[#F6F6F6]",
        )}
        onClick={() => handleCardClick(account, member, group)}
      >
        <div className="flex items-center justify-between">
          <BankIconMapping bankName={account?.branch} className="w-6" />
        </div>

        <div
          className={cn(
            "mt-1 text-xs flex gap-2",
            isCardSelected ? "text-[#905BB5]" : "text-foreground",
          )}
        >
          <span className="font-semibold text-xs">{account?.branch}</span>
          <span
            className={cn(
              "font-normal ",
              isCardSelected ? "text-[#905BB5]" : "text-[#797878]",
            )}
          >
            {account.maskedAccNumber || account.maskedAccNumber?.slice(-4)}
          </span>
        </div>
        <div
          className={cn(
            "text-xs font-normal flex gap-2",
            isCardSelected ? "text-[#AC80CC]" : "text-[#797878]",
          )}
        >
          <SyncIcon />
          <span
            className={cn(
              "text-xs font-normal",
              isCardSelected ? "text-[#AC80CC]" : "text-[#797878]",
            )}
          >
            {account.dataSyncedAt
              ? format(
                  new Date(parseInt(account.dataSyncedAt) * 1000),
                  "MMMM d, h:mmaaa",
                )
              : ""}
          </span>
        </div>
      </div>
    );
  };

  return (
    <>
      {members.length === 1 && groups.length === 0 ? (
        <div className="mt-3">
          <div className="flex gap-2 justify-start items-center text-sm font-medium">
            {members[0]!.name}
          </div>
          <div className="mt-4 flex overflow-x-auto gap-3">
            {members[0]!.accounts.map((account) => (
              <AccountCard
                key={account.accountId}
                account={account}
                member={members[0]}
              />
            ))}
          </div>
        </div>
      ) : (
        <Tabs defaultValue="members" className="mb-3">
          {groups.length > 1 && (
            <TabsList className="rounded-full border-none bg-[#F6F6F6] text-xs font-normal p-1.5">
              <TabsTrigger value="members" className="rounded-full">
                Members
              </TabsTrigger>
              {groups.length > 0 && (
                <TabsTrigger value="groups" className="rounded-full">
                  Groups
                </TabsTrigger>
              )}
            </TabsList>
          )}

          <TabsContent value="members" className="mt-3">
            <Accordion
              type="single"
              collapsible
              value={openItem}
              onValueChange={setOpenItem}
            >
              {members.map((member) => (
                <AccordionItem key={member.id} value={member.id}>
                  <AccordionTrigger>
                    <div className="flex gap-2 items-center text-sm font-medium">
                      <Checkbox
                        checked={member.accounts.some((acc) =>
                          selectedAccount.accountIds.has(acc.accountId),
                        )}
                        onCheckedChange={(checked) => {
                          handleMemberCheckboxChange(
                            member,
                            checked as boolean,
                          );

                          if (member.id === openItem) {
                            setTimeout(() => setOpenItem(member.id), 0);
                          } else if (checked) {
                            setOpenItem(member.id);
                          }
                        }}
                        onClick={(e) => e.stopPropagation()}
                        id={`member-${member.id}`}
                      />

                      {member.name}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="ml-6 mt-4 overflow-x-auto grid grid-cols-2 gap-3">
                      {member.accounts.map((account) => (
                        <AccountCard
                          key={account.accountId}
                          account={account}
                          member={member}
                        />
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </TabsContent>

          {groups.length > 0 && (
            <TabsContent value="groups" className="mt-3">
              <Accordion
                type="single"
                collapsible
                value={openItem}
                onValueChange={setOpenItem}
              >
                {groups.map((group) => (
                  <AccordionItem key={group.id} value={group.id}>
                    <AccordionTrigger>
                      <div className="flex flex-col w-full">
                        <div className="flex gap-2 items-center text-sm font-medium">
                          <Checkbox
                            checked={group.members.some((mem) =>
                              mem.accounts.some((acc) =>
                                selectedAccount.accountIds.has(acc.accountId!),
                              ),
                            )}
                            onCheckedChange={(checked) => {
                              handleGroupCheckboxChange(
                                group,
                                checked as boolean,
                              );

                              if (group.id === openItem) {
                                setTimeout(() => setOpenItem(group.id), 0);
                              } else if (checked) {
                                setOpenItem(group.id);
                              }
                            }}
                            onClick={(e) => e.stopPropagation()}
                            id={`group-${group.id}`}
                          />

                          {group.groupName}
                        </div>
                        <div className="pl-6 text-[#797878] text-xs mt-1 flex justify-start">
                          {groupMembersName(group.members)}
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="ml-6">
                        {group.members.map((member) => (
                          <div key={member.id}>
                            <div className="flex gap-2 justify-start items-start my-4">
                              {member.name}
                            </div>
                            <div className="ml-6 mt-4 overflow-x-auto grid grid-cols-2 gap-3">
                              {member.accounts.map((account) => (
                                <AccountCard
                                  key={account.accountId}
                                  account={account}
                                  group={group}
                                />
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </TabsContent>
          )}
        </Tabs>
      )}
    </>
  );
}

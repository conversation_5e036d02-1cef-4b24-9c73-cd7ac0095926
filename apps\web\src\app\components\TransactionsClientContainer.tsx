"use client";
import {
  Badge,
  FetchDocuments,
  FilterBadges,
  generateFilterBadgesFromFilters,
  TransactionActionFunction,
  TransactionCategory,
  TransactionsContainer,
  TransactionSearch,
  Category,
  Member,
  Group,
  getListFromParams,
} from "@repo/ui";
import { useState, useCallback, useEffect, useMemo } from "react";
import { firebaseStorage } from "../lib/firebase/config";
import { ref, uploadBytes } from "firebase/storage";
import { ExcludedCashFlowV2, GroupIcon } from "@repo/ui/icons";
import { TransactionGroup } from "@repo/ui";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { format, parseISO } from "date-fns";
import { usePagination } from "@repo/ui";

type TransactionsClientContainer = {
  initialTransactions: TransactionGroup[];
  initialPage: number;
  fetchMoreTransactions: (page: number) => Promise<TransactionGroup[]>;
  changeCategoryAction: (
    transactionId: string,
    categoryId: string,
    subCategoryId: string,
  ) => Promise<void>;
  updateTransaction: TransactionActionFunction;
  fetchTransactionDocuments: (transactionId: string) => Promise<FetchDocuments>;
  deleteTransactionDocuments: (
    transactionId: string,
    fileNames: string[],
  ) => Promise<{ ok: boolean }>;
  uploadFile?: (transactionId: string, file: File) => void;
  authToken?: string;
  phoneNumber: string;
  filters: {
    fromDate?: string;
    toDate?: string;
    minAmount?: string;
    maxAmount?: string;
    transactionType?: string;
    tagStatus?: string;
    bookmarkOptions?: {
      showFavorites?: boolean;
      excludedFromCashflow?: boolean;
      withNotes?: boolean;
    };
    categoryFilters?: TransactionCategory[];
  };
  members: Member[];
  groups: Group[];
  filter?: {
    user_groups?: {
      user_ids?: Array<{ id: string }>;
      user_group_ids?: Array<{ id: string }>;
    };
    account_filters?: {
      account_ids?: Array<{ id: string }>;
    };
    txn_filters?: {
      time_range?: {
        from_time?: string;
        to_time?: string;
      };
      amount_range?: {
        min_amount?: string;
        max_amount?: string;
      };
      favorited?: {
        favorited: boolean;
      };
      exclude_cash_flow?: {
        exclude_cash_flow: boolean;
      };
      notes?: {
        has_user_notes: boolean;
      };
      untagged?: {
        untagged: boolean;
      };
      txn_type?: {
        txn_type: string;
      };
      category?: TransactionCategory[];
    };
  };
  searchResults: number;
};

const defaultFilters = {
  selectedRange: undefined,
  selectedAmountRange: [0, 200000],
  selectedDateLabel: "All time",
  selectedMembers: [],
  selectedGroups: [],
  selectedAccounts: { accountIds: new Set<string>() },
  selectedBookmarkOptions: {
    showFavorites: false,
    excludedFromCashflow: false,
    withNotes: false,
  },
  selectedTags: [],
  selectedTransactionType: "All",
  selectedTagStatus: "All",
  selectedCategories: {},
  categoryFilters: [],
};

function TransactionsClientContainer({
  initialTransactions,
  initialPage,
  fetchMoreTransactions,
  changeCategoryAction,
  updateTransaction,
  fetchTransactionDocuments,
  deleteTransactionDocuments,
  phoneNumber,
  filters,
  filter,
  searchResults,
  authToken,
  members,
  groups,
}: TransactionsClientContainer) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [clearFilterFunction, setClearFilterFunction] = useState<
    ((value: string | undefined) => void) | null
  >(null);
  const [searchTerm, setSearchTerm] = useState<string | null>();
  const [searchCategory, setSearchcategory] = useState<string | null>();
  const [categoriesData, setCategoriesData] = useState<Category[]>([]);
  const [membersState, setMembersState] = useState<Member[]>(members);
  const [groupsState, setGroupsState] = useState<Group[]>(groups);

  const {
    data: transactions,
    lastItemRef,
    setPage,
    setHasMore,
    hasMore,
  } = usePagination({
    initialData: initialTransactions,
    initialPage,
    fetchMore: fetchMoreTransactions,
  });

  const handleCategoriesData = useCallback((categories: Category[]) => {
    setCategoriesData(categories);
  }, []);

  const handleUploadFile = (transactionId: string, file: File) => {
    const path = `${phoneNumber}/${transactionId}/${file?.name}`;
    const storageRef = ref(firebaseStorage, path);

    uploadBytes(storageRef, file).then((_snapshot) => {
      console.log("File Uploaded Successfully!!!");
    });
  };

  useEffect(() => {
    async function fetchData() {
      if (!authToken) {
        return;
      }
      try {
        const response = await fetch(
          `/api/memberAndGroup?authToken=${encodeURIComponent(authToken)}`,
        );
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(
            `Error fetching data: ${response.status} - ${errorText}`,
          );
        }
        const data = await response.json();
        if (data.members?.length > 0) {
          setMembersState(data.members);
        }
        if (data.groups?.length > 0) {
          setGroupsState(data.groups);
        }
      } catch (error) {
        console.error("Error fetching members and groups:", error);
      }
    }

    fetchData();
  }, [authToken]);

  const getInitialFilters = useCallback(() => {
    const formatDate = (dateString: string) =>
      format(parseISO(dateString), "MMM d");
    const fromDateParam = searchParams?.get("fromDate");
    const toDateParam = searchParams?.get("toDate");
    let dateRange = undefined;
    let dateLabel = "All time";
    if (fromDateParam && toDateParam) {
      const from = parseISO(fromDateParam);
      const to = parseISO(toDateParam);
      dateRange = { from, to };
      dateLabel = `${formatDate(fromDateParam)} - ${formatDate(toDateParam)}, ${format(parseISO(toDateParam), "yyyy")}`;
    }
    const minAmount = searchParams?.get("minAmount")
      ? parseInt(searchParams.get("minAmount")!)
      : 0;
    const maxAmount = searchParams?.get("maxAmount")
      ? parseInt(searchParams.get("maxAmount")!)
      : 200000;
    const transactionType = searchParams?.get("transactionType") || "All";

    const showFavorites = searchParams?.get("showFavorites") === "true";
    const excludedFromCashflow =
      searchParams?.get("excludedFromCashflow") === "true";
    const withNotes = searchParams?.get("withNotes") === "true";
    const tagStatus = searchParams?.get("tagStatus") || "All";
    let selectedMembers: string[] = [];
    let selectedGroups: string[] = [];
    if (searchParams !== null) {
      selectedMembers = getListFromParams(
        searchParams,
        "memberCount",
        "memberId",
      );
      selectedGroups = getListFromParams(searchParams, "groupCount", "groupId");
    }

    const updatedFilters = {
      ...defaultFilters,
      selectedRange: dateRange,
      selectedDateLabel: dateLabel,
      selectedAmountRange: [minAmount, maxAmount],
      selectedTransactionType: transactionType,
      selectedTagStatus: tagStatus,
      selectedBookmarkOptions: {
        showFavorites,
        excludedFromCashflow,
        withNotes,
      },
      categoryFilters: [] as TransactionCategory[],
      selectedCategories: {} as { [categoryId: string]: string[] },
      selectedMembers,
      selectedGroups,
      selectedAccounts: { accountIds: new Set<string>() },
    };
    const categoryFiltersCount = searchParams?.get("categoryFiltersCount");
    if (categoryFiltersCount) {
      const count = parseInt(categoryFiltersCount);
      const categoryFilters = Array.from({ length: count }, (_, i) => {
        const categoryId = searchParams?.get(`categoryId_${i}`);
        const subcategoryId = searchParams?.get(`subcategoryId_${i}`);

        return categoryId
          ? {
              categoryCollection: "",
              categoryId: categoryId,
              subcategoryId: subcategoryId || "",
            }
          : null;
      }).filter(Boolean) as TransactionCategory[];

      const selectedCategories = categoryFilters.reduce(
        (acc, filter) => {
          if (filter.categoryId) {
            if (!acc[filter.categoryId]) {
              acc[filter.categoryId] = [];
            }
            if (filter.subcategoryId) {
              acc[filter.categoryId]!.push(filter.subcategoryId);
            }
          }
          return acc;
        },
        {} as { [categoryId: string]: string[] },
      );

      updatedFilters.categoryFilters = categoryFilters;
      updatedFilters.selectedCategories = selectedCategories;
    }
    if (filter) {
      if (filter?.user_groups) {
        if (
          filter?.user_groups.user_ids &&
          filter?.user_groups.user_ids.length > 0
        ) {
          updatedFilters.selectedMembers = filter.user_groups.user_ids.map(
            (item) => item.id,
          );
        }

        if (
          filter.user_groups.user_group_ids &&
          filter.user_groups.user_group_ids.length > 0
        ) {
          updatedFilters.selectedGroups = filter.user_groups.user_group_ids.map(
            (item) => item.id,
          );
        }
      }
      if (filter.account_filters && filter.account_filters?.account_ids) {
        const accountIds = new Set<string>();
        filter.account_filters.account_ids.forEach((account) => {
          accountIds.add(account.id);
        });
        updatedFilters.selectedAccounts = { accountIds };
      }

      if (filter.txn_filters?.time_range) {
        const timeRange = filter.txn_filters.time_range;
        if (timeRange.from_time && timeRange.to_time) {
          const from = new Date(timeRange.from_time);
          const to = new Date(timeRange.to_time);
          updatedFilters.selectedRange = { from, to };
          updatedFilters.selectedDateLabel = `${formatDate(timeRange.from_time)} - ${formatDate(timeRange.to_time)}, ${format(new Date(timeRange.to_time), "yyyy")}`;
        }
      }
      if (filter.txn_filters?.amount_range) {
        const amountRange = filter.txn_filters.amount_range;
        if (
          amountRange.min_amount !== undefined &&
          amountRange.max_amount !== undefined
        ) {
          updatedFilters.selectedAmountRange = [
            parseInt(amountRange.min_amount),
            parseInt(amountRange.max_amount),
          ];
        }
      }
      if (filter?.txn_filters?.favorited) {
        updatedFilters.selectedBookmarkOptions.showFavorites =
          filter?.txn_filters?.favorited.favorited;
      }

      if (filter?.txn_filters?.exclude_cash_flow) {
        updatedFilters.selectedBookmarkOptions.excludedFromCashflow =
          filter?.txn_filters?.exclude_cash_flow.exclude_cash_flow;
      }
      if (filter?.txn_filters?.notes?.has_user_notes) {
        updatedFilters.selectedBookmarkOptions.withNotes =
          !!filter?.txn_filters?.notes?.has_user_notes;
      }

      if (filter?.txn_filters?.txn_type) {
        updatedFilters.selectedTransactionType =
          filter?.txn_filters?.txn_type.txn_type === "CREDIT"
            ? "Incoming"
            : "Outgoing";
      }
      if (filter?.txn_filters?.untagged !== undefined) {
        updatedFilters.selectedTagStatus = filter?.txn_filters?.untagged
          .untagged
          ? "Untagged"
          : "Tagged";
      }
    }

    return updatedFilters;
  }, [searchParams, filters, filter]);

  const [initialFilter] = useState(() => getInitialFilters());
  const [currentFilters, setCurrentFilters] = useState(initialFilter);
  const [filterBadges, setFilterBadges] = useState<string[]>([]);

  //updating badges
  useEffect(() => {
    if (categoriesData.length === 0) return;
    setFilterBadges(
      generateFilterBadgesFromFilters(
        currentFilters,
        categoriesData,
        membersState,
        groupsState,
      ),
    );
  }, [currentFilters, categoriesData, membersState, groupsState]);

  // Sync initialFilters with currentFilters
  useEffect(() => {
    setCurrentFilters(initialFilter);
  }, [initialFilter]);

  // For search Transactions
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);

    if (searchTerm && searchTerm.trim() !== "") {
      searchParams.set("search", searchTerm);
    } else {
      searchParams.delete("search");
    }

    if (searchCategory) {
      searchParams.set("searchCategory", searchCategory);
    } else {
      searchParams.delete("searchCategory");
    }

    router.push(`?${searchParams.toString()}`);
  }, [searchTerm, searchCategory]);

  // Note: resetPagination is no longer needed as usePagination hook
  // automatically updates when initialTransactions changes

  const appliedCount = useMemo(() => {
    let count = 0;
    if (
      currentFilters.selectedDateLabel !== "All time" &&
      currentFilters.selectedRange !== undefined
    ) {
      count++;
    }
    if (
      currentFilters.selectedAmountRange[0]! > 0 ||
      currentFilters.selectedAmountRange[1]! < 200000
    ) {
      count++;
    }
    if (
      currentFilters.selectedMembers.length > 0 ||
      currentFilters.selectedGroups.length > 0 ||
      currentFilters.selectedTags.length > 0
    ) {
      count++;
    }

    const bookmarks = currentFilters.selectedBookmarkOptions;
    if (bookmarks.showFavorites) {
      count++;
    }
    if (bookmarks.withNotes) {
      count++;
    }
    if (bookmarks.excludedFromCashflow) {
      count++;
    }
    if (currentFilters.selectedTagStatus !== "All") {
      count++;
    }
    if (currentFilters.selectedTransactionType !== "All") {
      count++;
    }

    if (Object.keys(currentFilters.selectedCategories).length > 0) {
      count++;
    }

    return count;
  }, [currentFilters]);

  const handleClearAllFilters = useCallback(() => {
    setCurrentFilters(defaultFilters);
    setFilterBadges([]);
    setHasMore(true);
    setPage(initialPage);

    if (!pathname) {
      router.push(pathname);
    }
  }, [router, pathname]);

  const handleFilterStateChange = useCallback((isOpen: boolean) => {
    setIsFilterOpen(isOpen);
  }, []);

  const handleFilterBadgesChange = useCallback(
    (badges: string[]) => {
      setFilterBadges(badges);

      const updatedFilters = getInitialFilters();
      setCurrentFilters(updatedFilters);
      setHasMore(true);
    },
    [getInitialFilters],
  );

  const handleApplyAllFilters = useCallback(
    async (filters: any) => {
      setCurrentFilters(filters);

      const searchParams = new URLSearchParams();
      if (filters.selectedMembers.length > 0) {
        searchParams.set(
          "memberCount",
          filters.selectedMembers.length.toString(),
        );
        Array.from({ length: filters.selectedMembers.length }, (_, i) => {
          searchParams.set(`memberId_${i}`, filters.selectedMembers[i]);
        });
        searchParams.set("memberMatch", "true");
      } else {
        searchParams.delete("memberCount");
        searchParams.delete("memberMatch");
      }
      if (filters.selectedGroups.length > 0) {
        searchParams.set(
          "groupCount",
          filters.selectedGroups.length.toString(),
        );
        Array.from({ length: filters.selectedGroups.length }, (_, i) => {
          searchParams.set(`groupId_${i}`, filters.selectedGroups[i]);
        });
        searchParams.set("groupMatch", "true");
      } else {
        searchParams.delete("groupCount");
        searchParams.delete("groupMatch");
      }
      const selectedAccountIds = [
        ...(filters.selectedAccounts?.accountIds || []),
      ];
      if (selectedAccountIds.length > 0) {
        searchParams.set("accountCount", selectedAccountIds.length.toString());
        Array.from({ length: selectedAccountIds.length }, (_, i) => {
          searchParams.set(`accountId_${i}`, selectedAccountIds[i]!);
        });
        searchParams.set("accountMatch", "true");
      } else {
        searchParams.delete("accountCount");
        searchParams.delete("accountMatch");
        const currentParams = new URLSearchParams(window.location.search);
        const accountCount = parseInt(currentParams.get("accountCount") || "0");
        Array.from({ length: accountCount }, (_, i) => {
          searchParams.delete(`accountId_${i}`);
        });
      }
      if (filters.selectedRange?.from && filters.selectedRange?.to) {
        const fromDateStr = format(filters.selectedRange.from, "yyyy-MM-dd");
        const toDateStr = format(filters.selectedRange.to, "yyyy-MM-dd");
        searchParams.set("fromDate", fromDateStr);
        searchParams.set("toDate", toDateStr);
      } else {
        searchParams.delete("fromDate");
        searchParams.delete("toDate");
      }

      // Amount range
      if (
        filters.selectedAmountRange &&
        (filters.selectedAmountRange[0] > 0 ||
          filters.selectedAmountRange[1] < 200000)
      ) {
        searchParams.set(
          "minAmount",
          filters.selectedAmountRange[0].toString(),
        );
        searchParams.set(
          "maxAmount",
          filters.selectedAmountRange[1].toString(),
        );
      } else {
        searchParams.delete("minAmount");
        searchParams.delete("maxAmount");
      }

      // Transaction type
      if (
        filters.selectedTransactionType &&
        filters.selectedTransactionType !== "All"
      ) {
        searchParams.set("transactionType", filters.selectedTransactionType);
      } else {
        searchParams.delete("transactionType");
      }

      // Tag status
      if (filters.selectedTagStatus && filters.selectedTagStatus !== "All") {
        searchParams.set("tagStatus", filters.selectedTagStatus);
      } else {
        searchParams.delete("tagStatus");
      }

      // Bookmark options
      if (filters.selectedBookmarkOptions.showFavorites) {
        searchParams.set("showFavorites", "true");
      } else {
        searchParams.delete("showFavorites");
      }

      if (filters.selectedBookmarkOptions.excludedFromCashflow) {
        searchParams.set("excludedFromCashflow", "true");
      } else {
        searchParams.delete("excludedFromCashflow");
      }

      if (filters.selectedBookmarkOptions.withNotes) {
        searchParams.set("withNotes", "true");
      } else {
        searchParams.delete("withNotes");
      }
      if (filters.categoryFilters && filters.categoryFilters.length > 0) {
        searchParams.set(
          "categoryFiltersCount",
          filters.categoryFilters.length.toString(),
        );

        Array.from({ length: filters.categoryFilters.length }, (_, i) => {
          const filter = filters.categoryFilters[i];
          if (filter.categoryId) {
            searchParams.set(`categoryId_${i}`, filter.categoryId);
            searchParams.set(`subcategoryId_${i}`, filter.subcategoryId || "");
          }
        });
      } else {
        searchParams.delete("categoryFiltersCount");
        const currentParams = new URLSearchParams(window.location.search);
        const categoryCount = parseInt(
          currentParams.get("categoryFiltersCount") || "0",
        );
        Array.from({ length: categoryCount }, (_, i) => {
          searchParams.delete(`categoryId_${i}`);
          searchParams.delete(`subcategoryId_${i}`);
        });
      }
      const query = searchParams.toString();
      const newUrl = query ? `/dashboard?${query}` : "/dashboard";
      router.push(newUrl);
      router.refresh(); // Force page refresh to apply filters
      setHasMore(true);
    },
    [router],
  );

  const handleFilterToggle = useCallback(() => {
    handleFilterStateChange(!isFilterOpen);
  }, [isFilterOpen, handleFilterStateChange]);

  const handleClearFilters = useCallback(
    (fn: (value: string) => void) => {
      setClearFilterFunction(() => (value?: string) => {
        if (!value) return;
        if (value === "ALL") {
          handleClearAllFilters();
          return;
        }

        fn(value);
      });
    },
    [handleClearAllFilters],
  );

  return (
    <div className="flex flex-col w-full h-full">
      <div className="flex w-full items-center justify-between p-3 mt-6">
        <div className="w-4/5 place-items-center ml-[11rem]">
          <TransactionSearch
            onSearch={(query) => setSearchTerm(query)}
            onSearchCategoryChange={(category) => setSearchcategory(category)}
          />
        </div>
        <div className="flex gap-5 w-1/5 justify-end mr-4">
          <Badge
            selected={false}
            className="bg-[#BDE4B2] py-2 px-[10.2px] rounded-[100px]"
          >
            <div className="flex gap-2 items-center">
              <span className="text-xs text-[#2A5024]">Cashflow</span>
              <ExcludedCashFlowV2 className="w-6 h-6" fill="#2A5024" />
            </div>
          </Badge>
          <div
            className="w-[29.9px] h-[29.9px] bg-[#DFECFF] rounded-[78.371px] flex items-center justify-center cursor-pointer"
            onClick={handleFilterToggle}
          >
            <GroupIcon />
            {appliedCount > 0 && (
              <svg
                width="21"
                height="21"
                viewBox="0 0 21 21"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="absolute translate-x-[40%] -translate-y-[40%] w-5 h-5 rounded-full flex items-center justify-center z-10"
              >
                <g id="Group 1000002379">
                  <rect
                    x="1.28947"
                    y="1.28947"
                    width="18.4211"
                    height="18.4211"
                    rx="9.21053"
                    fill="#FEB737"
                    stroke=""
                    strokeWidth="1.57895"
                  />
                  <path
                    stroke="#FEB737"
                    strokeWidth="1.57895"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  {appliedCount !== undefined && (
                    <text
                      x="10.5"
                      y="14"
                      textAnchor="middle"
                      fill="black"
                      fontSize="10"
                      fontWeight="medium"
                    >
                      {appliedCount}
                    </text>
                  )}
                </g>
              </svg>
            )}
          </div>
        </div>
      </div>

      {searchTerm && (
        <div className="flex justify-between ml-4 mr-6 p-4 rounded-lg bg-[#905BB5] text-[#FBFBFB]">
          <div>
            Showing <b>All results</b> for "<b>{searchTerm}</b>"<br />
          </div>
          <div>
            <b>{searchResults} Transactions</b>
          </div>
        </div>
      )}

      {filterBadges?.length > 0 && (
        <div className="px-4 mt-6 w-full">
          <FilterBadges
            badges={filterBadges!}
            onRemoveFilter={(value) => {
              if (clearFilterFunction) {
                clearFilterFunction(value);
              }
            }}
          />
        </div>
      )}
      <TransactionsContainer
        transactions={transactions}
        changeCategoryAction={changeCategoryAction}
        updateTransaction={updateTransaction}
        fetchTransactionDocuments={fetchTransactionDocuments}
        deleteTransactionDocuments={deleteTransactionDocuments}
        uploadFile={handleUploadFile}
        defaultFilterState={isFilterOpen}
        initialFilters={currentFilters}
        onFilterStateChange={handleFilterStateChange}
        onFilterBadgesChange={handleFilterBadgesChange}
        onClearFilterFunc={handleClearFilters}
        onApplyAllFilters={handleApplyAllFilters}
        onResetUrlFilters={() => {
          if (!pathname) router.push(pathname);
          handleClearAllFilters();
        }}
        lastItemRef={lastItemRef}
        hasMore={hasMore}
        onCategoriesData={handleCategoriesData}
        members={membersState}
        groups={groupsState}
      />
    </div>
  );
}

export default TransactionsClientContainer;
